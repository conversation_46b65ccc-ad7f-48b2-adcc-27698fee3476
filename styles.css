/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-brown: #8B4513;
    --dark-brown: #654321;
    --light-beige: #F5F5DC;
    --cream: #FFF8DC;
    --accent-red: #DC143C;
    --accent-orange: #FF6B35;
    --accent-green: #90EE90;
    --accent-teal: #20B2AA;
    --text-dark: #2C2C2C;
    --text-light: #666666;
    --white: #FFFFFF;
    --border-color: #D2B48C;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.2rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: 2px solid var(--text-dark);
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    min-height: 48px;
    gap: 8px;
    background: transparent;
    color: var(--text-dark);
}

.btn:hover {
    background: var(--text-dark);
    color: var(--white);
}

.btn-primary {
    background: var(--accent-red);
    color: var(--white);
    border-color: var(--accent-red);
}

.btn-primary:hover {
    background: var(--dark-brown);
    border-color: var(--dark-brown);
}

.btn-outline {
    background: transparent;
    color: var(--text-dark);
    border: 2px solid var(--text-dark);
}

.btn-outline:hover {
    background: var(--text-dark);
    color: var(--white);
}

/* Top Banner */
.top-banner {
    background: var(--dark-brown);
    color: var(--white);
    padding: 8px 0;
    text-align: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    font-size: 0.9rem;
    font-weight: 500;
}

.banner-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.banner-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.banner-text i {
    color: var(--accent-orange);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 40px;
    left: 0;
    right: 0;
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.logo-line {
    height: 3px;
    background: var(--text-dark);
    border-radius: 2px;
}

.logo-line.line-1 { width: 20px; }
.logo-line.line-2 { width: 25px; }
.logo-line.line-3 { width: 15px; }

.logo-icon.large .logo-line.line-1 { width: 40px; }
.logo-icon.large .logo-line.line-2 { width: 50px; }
.logo-icon.large .logo-line.line-3 { width: 30px; }

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
    letter-spacing: 2px;
}

.logo-text p {
    font-size: 0.7rem;
    color: var(--text-light);
    margin: 0;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.logo-text.large h1 {
    font-size: 3rem;
    letter-spacing: 4px;
}

.logo-text.large p {
    font-size: 1rem;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 4px;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.nav-link:hover {
    color: var(--accent-red);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 2rem;
    min-width: 600px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1001;
}

.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.dropdown-column h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.dropdown-column a {
    display: block;
    color: var(--text-light);
    text-decoration: none;
    padding: 4px 0;
    transition: var(--transition);
    font-size: 0.9rem;
}

.dropdown-column a:hover {
    color: var(--accent-red);
}

/* Nav Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-icon, .cart {
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
    color: var(--text-dark);
}

.user-icon:hover, .cart:hover {
    background: var(--light-beige);
}

.cart {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--accent-red);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.coach-btn {
    background: var(--accent-red);
    color: var(--white) !important;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    text-decoration: none;
    transition: var(--transition);
}

.coach-btn:hover {
    background: var(--dark-brown);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    transition: var(--transition);
}

/* Hero Section */
.hero-section {
    background: var(--dark-brown);
    color: var(--white);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(101, 67, 33, 0.9) 0%, rgba(139, 69, 19, 0.8) 100%);
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.hero-title .highlight {
    color: var(--accent-red);
    position: relative;
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-red);
    border-radius: 2px;
}

.hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--white);
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.hero-stats .stat {
    text-align: center;
}

.hero-stats .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-red);
    margin-bottom: 0.5rem;
}

.hero-stats .stat-label {
    font-size: 0.9rem;
    color: var(--white);
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
}

.hero-image-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.main-hero-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: var(--white);
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    bottom: 20%;
    left: -15%;
    animation-delay: 1s;
}

.floating-card.card-3 {
    top: 50%;
    right: -5%;
    animation-delay: 2s;
}

.floating-card .card-icon {
    width: 40px;
    height: 40px;
    background: var(--accent-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.floating-card .card-icon i {
    color: var(--white);
    font-size: 1.2rem;
}

.floating-card .card-text {
    display: flex;
    flex-direction: column;
}

.floating-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.floating-card .card-subtitle {
    font-size: 0.8rem;
    color: var(--text-light);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.air-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 2px;
    background: var(--white);
}

.water-icon {
    width: 30px;
    height: 20px;
    position: relative;
}

.water-icon::before,
.water-icon::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 2px;
    background: var(--white);
    border-radius: 2px;
}

.water-icon::before {
    top: 6px;
}

.water-icon::after {
    top: 12px;
}

.element-item span {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 1px;
}

/* Hero Content Section */
.hero-content {
    background: var(--light-beige);
    padding: 80px 0;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.1;
}

.highlight {
    color: var(--text-dark);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-green);
    border: 2px solid var(--accent-green);
    border-radius: 2px;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.search-section {
    margin-top: 2rem;
}

.search-bar {
    display: flex;
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
}

.search-bar input {
    flex: 1;
    padding: 16px 20px;
    border: none;
    font-size: 1rem;
    outline: none;
}

.search-btn {
    background: var(--accent-red);
    color: var(--white);
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--dark-brown);
}

.feature-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.indicator-dots {
    display: flex;
    gap: 6px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.dot.red { background: var(--accent-red); }
.dot.orange { background: var(--accent-orange); }
.dot.teal { background: var(--accent-teal); }

.hero-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 8px;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* About Section */
.about-section {
    background: var(--white);
    padding: 80px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.about-subtitle {
    font-size: 1.2rem;
    color: var(--accent-red);
    font-weight: 600;
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-red);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.about-video {
    display: flex;
    justify-content: center;
    align-items: center;
}

.video-placeholder {
    width: 100%;
    height: 300px;
    background: var(--light-beige);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    border: 2px dashed var(--border-color);
}

.video-placeholder:hover {
    background: var(--cream);
    border-color: var(--accent-red);
}

.video-placeholder i {
    font-size: 4rem;
    color: var(--accent-red);
    margin-bottom: 1rem;
}

.video-placeholder p {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 600;
}

/* Real Testimonials */
.real-testimonials {
    background: var(--light-beige);
    padding: 80px 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.testimonial-card.featured {
    border: 2px solid var(--accent-red);
    transform: scale(1.02);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.testimonial-card.featured:hover {
    transform: scale(1.02) translateY(-5px);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content .stars {
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-dark);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h4 {
    margin-bottom: 0.25rem;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.author-info span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.verified {
    font-size: 0.8rem !important;
    color: var(--accent-green) !important;
    margin-top: 0.25rem;
}

/* Featured Coaches Compact */
.featured-coaches-compact {
    background: var(--white);
    padding: 60px 0;
}

.coaches-compact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.coach-compact {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-beige);
    border-radius: 12px;
    transition: var(--transition);
}

.coach-compact:hover {
    background: var(--cream);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.coach-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.coach-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coach-details h4 {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.coach-details p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.coach-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.coach-rating .stars {
    display: flex;
    gap: 2px;
}

.coach-rating .stars i {
    color: #ffc107;
    font-size: 0.8rem;
}

.coach-rating span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 600;
}

/* Benefits Section */
.benefits {
    background: var(--light-beige);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
}

.benefit-card {
    background: var(--white);
    padding: 3rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.benefit-card h3 {
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-size: 1.3rem;
}

.benefit-card p {
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
}

.benefit-card ul {
    list-style: none;
    margin-bottom: 2rem;
}

.benefit-card li {
    padding: 8px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.benefit-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-green);
    font-weight: bold;
}

/* How You Benefit Section */
.how-you-benefit {
    background: var(--white);
    padding: 80px 0;
}

.benefit-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.benefit-type {
    background: var(--light-beige);
    padding: 3rem;
    border-radius: 16px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.benefit-type::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-red);
}

.benefit-type:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.benefit-type .benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    border: none;
}

.benefit-type .benefit-icon i {
    font-size: 2rem;
    color: var(--white);
}

.benefit-type h3 {
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.benefit-subtitle {
    color: var(--accent-red);
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.benefit-type ul {
    list-style: none;
    margin-bottom: 2rem;
}

.benefit-type li {
    padding: 12px 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
    line-height: 1.6;
}

.benefit-type li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-green);
    font-weight: bold;
    font-size: 1.1rem;
}

.benefit-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 12px;
}

.stat {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-red);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Testimonials */
.testimonials {
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--light-beige);
    padding: 2rem;
    border-radius: 8px;
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-dark);
}

.testimonial-author h4 {
    margin-bottom: 0.25rem;
    color: var(--text-dark);
    text-transform: lowercase;
}

.testimonial-author span {
    font-size: 0.9rem;
    color: var(--text-light);
}

.verified {
    font-size: 0.8rem !important;
    color: var(--text-light) !important;
    margin-top: 0.5rem;
}

/* Featured Products */
.featured-products {
    background: var(--light-beige);
    padding: 80px 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card.featured {
    border: 2px solid var(--accent-red);
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(220, 20, 60, 0.2);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-card.featured:hover {
    transform: scale(1.02) translateY(-10px);
    box-shadow: 0 20px 50px rgba(220, 20, 60, 0.3);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--accent-red);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    color: var(--accent-red);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.product-info h3 {
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-size: 1.1rem;
    line-height: 1.4;
    font-weight: 600;
}

.product-description {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-tag {
    background: var(--light-beige);
    color: var(--text-dark);
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
}

.price-range {
    font-size: 0.9rem;
    color: var(--text-light);
}

.original-price {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.savings {
    background: var(--accent-green);
    color: var(--white);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.product-rating .stars {
    display: flex;
    gap: 2px;
}

.product-rating .stars i {
    color: #ffc107;
    font-size: 0.8rem;
}

.rating-count {
    font-size: 0.8rem;
    color: var(--text-light);
}

.products-footer {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.shipping-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.shipping-info i {
    color: var(--accent-green);
}

/* Newsletter */
.newsletter {
    background: linear-gradient(135deg, var(--light-brown) 0%, var(--medium-brown) 100%);
    padding: 4rem 0;
    text-align: center;
}

.newsletter-content h2 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 2.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto 1rem;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-dark);
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--accent-red);
}

.newsletter-disclaimer {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 1rem;
}

/* Footer */
.footer {
    background: var(--dark-brown);
    color: var(--white);
    padding: 2rem 0 0.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1rem;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.footer-brand {
    max-width: 300px;
}

.footer-description {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--accent-orange);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
}

.footer-logo .logo-text h3 {
    color: var(--white);
    margin: 0;
    font-size: 1.2rem;
    letter-spacing: 2px;
}

.footer-logo .logo-text p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.7rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.footer-logo .logo-line {
    background: var(--white);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-orange);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-menu {
        gap: 1.5rem;
    }
    
    .dropdown-content {
        min-width: 500px;
    }
    
    .dropdown-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .elements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .top-banner {
        font-size: 0.8rem;
        padding: 6px 0;
    }
    
    .navbar {
        top: 32px;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .hamburger {
        display: flex;
    }
    
    .dropdown-content {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        padding: 1rem 0;
        min-width: auto;
    }
    
    .dropdown-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .elements-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .coaches-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .top-banner {
        font-size: 0.7rem;
        padding: 4px 0;
    }
    
    .navbar {
        top: 28px;
    }
    
    .elemental-section {
        padding: 100px 0 60px;
    }
    
    .hero-content {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-container {
        padding: 0 1rem;
    }
    
    section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    .benefit-card {
        padding: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .product-info {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content > * {
    animation: fadeInUp 0.8s ease-out forwards;
}

.hero-content > *:nth-child(1) { animation-delay: 0.1s; }
.hero-content > *:nth-child(2) { animation-delay: 0.2s; }
.hero-content > *:nth-child(3) { animation-delay: 0.3s; }
.hero-content > *:nth-child(4) { animation-delay: 0.4s; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
input:focus {
    outline: 2px solid var(--accent-red);
    outline-offset: 2px;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
        box-shadow: none;
        padding: 1rem 0;
        min-width: auto;
    }
    
    .dropdown-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .elements-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .coaches-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .top-banner {
        font-size: 0.7rem;
        padding: 4px 0;
    }
    
    .navbar {
        top: 28px;
    }
    
    .elemental-section {
        padding: 100px 0 60px;
    }
    
    .hero-content {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-container {
        padding: 0 1rem;
    }
    
    section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    .benefit-card {
        padding: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .product-info {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content > * {
    animation: fadeInUp 0.8s ease-out forwards;
}

.hero-content > *:nth-child(1) { animation-delay: 0.1s; }
.hero-content > *:nth-child(2) { animation-delay: 0.2s; }
.hero-content > *:nth-child(3) { animation-delay: 0.3s; }
.hero-content > *:nth-child(4) { animation-delay: 0.4s; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
input:focus {
    outline: 2px solid var(--accent-red);
    outline-offset: 2px;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.footer-logo .logo-line {
    background: var(--white);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-orange);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-menu {
        gap: 1.5rem;
    }
    
    .dropdown-content {
        min-width: 500px;
    }
    
    .dropdown-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .elements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .top-banner {
        font-size: 0.8rem;
        padding: 6px 0;
    }
    
    .navbar {
        top: 32px;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .hamburger {
        display: flex;
    }
    
    .dropdown-content {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        padding: 1rem 0;
        min-width: auto;
    }
    
    .dropdown-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .elements-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .coaches-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .top-banner {
        font-size: 0.7rem;
        padding: 4px 0;
    }
    
    .navbar {
        top: 28px;
    }
    
    .elemental-section {
        padding: 100px 0 60px;
    }
    
    .hero-content {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-container {
        padding: 0 1rem;
    }
    
    section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    .benefit-card {
        padding: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .product-info {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content > * {
    animation: fadeInUp 0.8s ease-out forwards;
}

.hero-content > *:nth-child(1) { animation-delay: 0.1s; }
.hero-content > *:nth-child(2) { animation-delay: 0.2s; }
.hero-content > *:nth-child(3) { animation-delay: 0.3s; }
.hero-content > *:nth-child(4) { animation-delay: 0.4s; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
input:focus {
    outline: 2px solid var(--accent-red);
    outline-offset: 2px;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
        box-shadow: none;
        padding: 1rem 0;
        min-width: auto;
    }
    
    .dropdown-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .elements-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .coaches-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .top-banner {
        font-size: 0.7rem;
        padding: 4px 0;
    }
    
    .navbar {
        top: 28px;
    }
    
    .elemental-section {
        padding: 100px 0 60px;
    }
    
    .hero-content {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-container {
        padding: 0 1rem;
    }
    
    section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    .benefit-card {
        padding: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .product-info {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content > * {
    animation: fadeInUp 0.8s ease-out forwards;
}

.hero-content > *:nth-child(1) { animation-delay: 0.1s; }
.hero-content > *:nth-child(2) { animation-delay: 0.2s; }
.hero-content > *:nth-child(3) { animation-delay: 0.3s; }
.hero-content > *:nth-child(4) { animation-delay: 0.4s; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
input:focus {
    outline: 2px solid var(--accent-red);
    outline-offset: 2px;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }